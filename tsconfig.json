{
  // https://nuxt.com/docs/guide/concepts/typescript
  "extends": "./.nuxt/tsconfig.json",
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Node",
    "strict": true,
    "jsx": "preserve",
    "esModuleInterop": true,
    "skipLibCheck": true
  },
  "include": [
    "**/*.ts",
    "**/*.vue",
    "nuxt.config.ts"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}
