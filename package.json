{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "0.7.4", "@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/tailwindcss": "6.12.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.469.0", "nuxt": "^3.15.0", "radix-vue": "^1.9.12", "shadcn-nuxt": "0.11.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vue": "^3.5.13", "vue-router": "4.5.0"}, "packageManager": "pnpm@9.15.2+sha512.93e57b0126f0df74ce6bff29680394c0ba54ec47246b9cf321f0121d8d9bb03f750a705f24edc3c1180853afd7c2c3b94196d0a3d53d3e069d9e2793ef11f321", "devDependencies": {"@antfu/eslint-config": "^3.12.1", "@typescript-eslint/eslint-plugin": "^8.19.0", "@typescript-eslint/parser": "^8.19.0", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "sass": "^1.83.0", "typescript": "^5.7.2"}}