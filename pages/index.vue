<script lang="ts" setup>
	import { But<PERSON> } from '@/components/ui/button'
	import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
	import type { User } from '@/interfaces/index'
</script>

<template>
	<div class="">
		<Button>Ahihi</Button>

		<Dialog>
			<DialogTrigger>
				<Button variant="outline"> Edit Profile </Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Edit profile</DialogTitle>
					<DialogDescription> Make changes to your profile here. Click save when you're done. </DialogDescription>
				</DialogHeader>

				<DialogFooter>
					<Button> Save changes </Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	</div>
</template>

<style lang="scss" scoped></style>
