<script lang="ts" setup>
	import { ref } from 'vue'
	import { Button } from '@/components/ui/button'
	import { Input } from '@/components/ui/input'
	import { Textarea } from '@/components/ui/textarea'
	import { Label } from '@/components/ui/label'
	import { Select } from '@/components/ui/select'

	// Form data
	const formData = ref({
		area: '',
		securityLevel: '',
		subject: '',
		description: ''
	})

	// Select options
	const areaOptions = [
		{ value: 'billing', label: 'Billing' },
		{ value: 'technical', label: 'Technical' },
		{ value: 'account', label: 'Account' },
		{ value: 'general', label: 'General' }
	]

	const securityLevelOptions = [
		{ value: 'severity1', label: 'Severity 1' },
		{ value: 'severity2', label: 'Severity 2' },
		{ value: 'severity3', label: 'Severity 3' },
		{ value: 'severity4', label: 'Severity 4' }
	]

	// Form handlers
	const handleSubmit = () => {
		console.log('Form submitted:', formData.value)
		// Handle form submission here
	}

	const handleCancel = () => {
		// Reset form
		formData.value = {
			area: '',
			securityLevel: '',
			subject: '',
			description: ''
		}
	}
</script>

<template>
	<div class="flex min-h-screen items-center justify-center bg-gray-50 p-4">
		<div class="w-full max-w-lg rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
			<!-- Header -->
			<div class="mb-6">
				<h1 class="mb-2 text-xl font-semibold text-gray-900">Report an issue</h1>
				<p class="text-sm text-gray-600">What area are you having problems with?</p>
			</div>

			<!-- Form -->
			<form @submit.prevent="handleSubmit" class="space-y-6">
				<!-- Area and Security Level Row -->
				<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
					<!-- Area -->
					<div class="space-y-2">
						<Label for="area" class="text-sm font-medium text-gray-700">Area</Label>
						<Select id="area" v-model="formData.area" :options="areaOptions" placeholder="Billing" class="w-full" />
					</div>

					<!-- Security Level -->
					<div class="space-y-2">
						<Label for="security-level" class="text-sm font-medium text-gray-700">Security Level</Label>
						<Select id="security-level" v-model="formData.securityLevel" :options="securityLevelOptions" placeholder="Severity 2" class="w-full" />
					</div>
				</div>

				<!-- Subject -->
				<div class="space-y-2">
					<Label for="subject" class="text-sm font-medium text-gray-700">Subject</Label>
					<Input id="subject" v-model="formData.subject" placeholder="I need help with..." class="w-full" />
				</div>

				<!-- Description -->
				<div class="space-y-2">
					<Label for="description" class="text-sm font-medium text-gray-700">Description</Label>
					<Textarea id="description" v-model="formData.description" placeholder="Please include all information relevant to your issue." :rows="6" class="w-full resize-none" />
				</div>

				<!-- Form Actions -->
				<div class="flex justify-end gap-3 pt-4">
					<Button type="button" variant="outline" @click="handleCancel" class="px-6"> Cancel </Button>
					<Button type="submit" class="bg-black px-6 text-white hover:bg-gray-800"> Submit </Button>
				</div>
			</form>
		</div>
	</div>
</template>

<style lang="scss" scoped></style>
