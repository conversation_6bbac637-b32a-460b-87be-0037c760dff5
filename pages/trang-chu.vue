<template>
	<div>
		<!-- Hero Section -->
		<section class="relative h-[600px] w-full overflow-hidden">
			<img src="/images/hero-bg.png" alt="Hero" class="absolute inset-0 z-0 h-full w-full object-cover" />
			<div class="absolute inset-0 z-10 flex flex-col items-center justify-center bg-gradient-to-r from-green-800/60 to-green-500/30 p-6 text-center text-white">
				<h1 class="mb-4 text-4xl font-bold md:text-6xl">V<PERSON> cuộc sống tươi đẹp</h1>
				<p class="text-lg md:text-2xl">Bệnh viện Mắt Thái Hà Hòa Bình</p>
				<div class="mt-6 flex flex-wrap justify-center gap-3">
					<Select placeholder="Họ và tên" class="w-52" />
					<Select placeholder="Chọn dịch vụ" class="w-52" />
					<Input placeholder="Nhập SĐT" class="w-52" />
					<Button variant="success"><PERSON><PERSON><PERSON> ký</Button>
				</div>
			</div>
		</section>

		<!-- Tại sao chọn bệnh viện -->
		<section class="bg-gray-50 py-16">
			<div class="container mx-auto text-center">
				<h2 class="mb-10 text-3xl font-bold text-green-800">TẠI SAO CHỌN BỆNH VIỆN MẮT THÁI HÀ?</h2>
				<div class="grid gap-6 md:grid-cols-3">
					<Card v-for="(reason, index) in reasons" :key="index" class="p-6">
						<img :src="reason.image" alt="" class="mx-auto mb-4 h-20 w-20 rounded-full object-cover" />
						<h3 class="mb-2 text-xl font-semibold">{{ reason.title }}</h3>
						<p class="text-sm text-gray-600">{{ reason.desc }}</p>
					</Card>
				</div>
			</div>
		</section>

		<!-- Khách hàng nói về chúng tôi -->
		<section class="bg-green-50 py-16">
			<div class="container mx-auto text-center">
				<h2 class="mb-8 text-3xl font-bold text-green-800">KHÁCH HÀNG NÓI VỀ CHÚNG TÔI</h2>
				<div class="flex gap-6 overflow-x-auto px-4">
					<Card v-for="(feedback, index) in feedbacks" :key="index" class="w-72 flex-shrink-0 p-4">
						<img :src="feedback.image" alt="" class="mx-auto mb-4 h-20 w-20 rounded-full" />
						<p class="text-sm text-gray-700">{{ feedback.content }}</p>
						<h4 class="mt-2 font-bold text-green-800">{{ feedback.name }}</h4>
					</Card>
				</div>
			</div>
		</section>

		<!-- Dịch vụ mũi nhọn -->
		<section class="bg-white py-16">
			<div class="container mx-auto text-center">
				<h2 class="mb-10 text-3xl font-bold text-green-800">DỊCH VỤ MŨI NHỌN</h2>
				<div class="grid gap-6 md:grid-cols-3">
					<Card v-for="(service, index) in services" :key="index" class="p-6">
						<img :src="service.image" alt="" class="mb-4 h-36 w-full rounded-xl object-cover" />
						<h3 class="mb-2 text-xl font-semibold">{{ service.title }}</h3>
						<Button variant="secondary" class="mt-3">Xem thêm</Button>
					</Card>
				</div>
			</div>
		</section>

		<!-- Cẩm nang khách hàng -->
		<section class="bg-gray-50 py-16">
			<div class="container mx-auto text-center">
				<h2 class="mb-10 text-3xl font-bold text-green-800">CẨM NANG KHÁCH HÀNG</h2>
				<div class="grid gap-6 md:grid-cols-3">
					<Card v-for="(tip, index) in tips" :key="index" class="p-6">
						<img :src="tip.image" alt="" class="mb-4 h-36 w-full rounded-xl object-cover" />
						<h3 class="mb-2 text-lg font-semibold">{{ tip.title }}</h3>
						<Button variant="secondary" class="mt-3">Xem thêm</Button>
					</Card>
				</div>
			</div>
		</section>

		<!-- Tư vấn -->
		<section class="bg-green-100 py-16">
			<div class="container mx-auto text-center">
				<h2 class="mb-6 text-3xl font-bold text-green-800">ĐẶT LỊCH TƯ VẤN NGAY HÔM NAY!</h2>
				<div class="flex flex-wrap justify-center gap-3">
					<Input placeholder="Họ và tên" class="w-52" />
					<Select placeholder="Chọn dịch vụ" class="w-52" />
					<Input placeholder="Nhập số điện thoại" class="w-52" />
					<Button variant="success">Đăng ký</Button>
				</div>
			</div>
		</section>
	</div>
</template>

<script setup lang="ts">
	import { ref } from 'vue'
	import { Card } from '@/components/ui/card'
	import { Button } from '@/components/ui/button'
	import { Input } from '@/components/ui/input'
	import { Select } from '@/components/ui/select'

	const reasons = ref([
		{
			image: '/images/reason1.png',
			title: 'Chuyên gia đầu ngành',
			desc: 'Bệnh viện hội tụ đội ngũ chuyên gia, bác sĩ đầu ngành nhãn khoa.'
		},
		{
			image: '/images/reason2.png',
			title: 'Trang thiết bị hiện đại',
			desc: 'Hệ thống máy móc tiên tiến nhập khẩu từ Mỹ, Nhật Bản, Đức...'
		},
		{
			image: '/images/reason3.png',
			title: 'Chăm sóc tận tâm',
			desc: 'Đội ngũ điều dưỡng hỗ trợ nhiệt tình, chu đáo với khách hàng.'
		}
	])

	const feedbacks = ref([
		{
			image: '/images/customer1.png',
			name: 'Ông Văn Cường',
			content: 'Tôi rất hài lòng với dịch vụ và đội ngũ bác sĩ ở đây.'
		},
		{
			image: '/images/customer2.png',
			name: 'Bà Mai Lan',
			content: 'Phẫu thuật nhanh chóng, không đau, hồi phục tốt.'
		},
		{
			image: '/images/customer3.png',
			name: 'Cháu Ngọc Anh',
			content: 'Dịch vụ khám cho trẻ em rất chu đáo và chuyên nghiệp.'
		}
	])

	const services = ref([
		{
			image: '/images/service1.png',
			title: 'Phẫu thuật Glô-côm'
		},
		{
			image: '/images/service2.png',
			title: 'Phẫu thuật đục thủy tinh thể'
		},
		{
			image: '/images/service3.png',
			title: 'Phẫu thuật khúc xạ'
		}
	])

	const tips = ref([
		{
			image: '/images/tip1.png',
			title: 'Bảo hiểm khi khám mắt'
		},
		{
			image: '/images/tip2.png',
			title: 'Thông tin hữu ích về điều trị'
		},
		{
			image: '/images/tip3.png',
			title: 'Khuyến mãi dịch vụ'
		}
	])
</script>

<style scoped>
	.container {
		@apply mx-auto max-w-7xl px-4;
	}
</style>
