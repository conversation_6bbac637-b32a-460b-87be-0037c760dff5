<script lang="ts" setup>
	import { ref } from 'vue'

	// Hero section data
	const heroData = ref({
		title: '<PERSON><PERSON> sức khỏe',
		subtitle: '<PERSON><PERSON> cuộc sống',
		description: 'Chúng tôi cam kết mang đến dịch vụ y tế chất lượng cao với đội ngũ bác sĩ giàu kinh nghiệm và trang thiết bị hiện đại.'
	})

	// Services data
	const services = ref([
		{
			id: 1,
			title: '<PERSON>h<PERSON><PERSON> sức khỏe tổng quát',
			description: '<PERSON><PERSON><PERSON> khám sức khỏe toàn diện với các xét nghiệm cơ bản và chuyên sâu',
			image: '/images/service-1.jpg',
			link: '#'
		},
		{
			id: 2,
			title: 'Chẩn đoán hình ảnh',
			description: '<PERSON><PERSON><PERSON> âm, X-quang, CT, MRI với thiết bị hiện đại và chính xác cao',
			image: '/images/service-2.jpg',
			link: '#'
		},
		{
			id: 3,
			title: '<PERSON><PERSON><PERSON> nghiệm y học',
			description: '<PERSON><PERSON><PERSON> nghiệm máu, nước tiểu và các xét nghiệm chuyên khoa khác',
			image: '/images/service-3.jpg',
			link: '#'
		}
	])

	// Doctors data
	const doctors = ref([
		{
			id: 1,
			name: 'BS. Nguyễn Văn A',
			specialty: 'Chuyên khoa Tim mạch',
			experience: '15 năm kinh nghiệm',
			image: '/images/doctor-1.jpg'
		},
		{
			id: 2,
			name: 'BS. Trần Thị B',
			specialty: 'Chuyên khoa Nhi',
			experience: '12 năm kinh nghiệm',
			image: '/images/doctor-2.jpg'
		},
		{
			id: 3,
			name: 'BS. Lê Văn C',
			specialty: 'Chuyên khoa Ngoại',
			experience: '18 năm kinh nghiệm',
			image: '/images/doctor-3.jpg'
		}
	])

	// News data
	const news = ref([
		{
			id: 1,
			title: 'Cách phòng ngừa bệnh tim mạch hiệu quả',
			excerpt: 'Những biện pháp đơn giản giúp bảo vệ sức khỏe tim mạch của bạn',
			image: '/images/news-1.jpg',
			date: '15/12/2024'
		},
		{
			id: 2,
			title: 'Tầm quan trọng của việc khám sức khỏe định kỳ',
			excerpt: 'Khám sức khỏe định kỳ giúp phát hiện sớm các bệnh lý nguy hiểm',
			image: '/images/news-2.jpg',
			date: '12/12/2024'
		}
	])

	// Contact form
	const contactForm = ref({
		name: '',
		phone: '',
		email: '',
		message: ''
	})

	const submitContact = () => {
		console.log('Contact form submitted:', contactForm.value)
		// Handle form submission
	}
</script>

<template>
	<div class="min-h-screen bg-white">
		<!-- Header -->
		<header class="border-b bg-white shadow-sm">
			<div class="container mx-auto px-4">
				<!-- Top bar -->
				<div class="flex items-center justify-between border-b py-2 text-sm">
					<div class="flex items-center space-x-4 text-gray-600">
						<div class="flex items-center space-x-1">
							<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
								></path>
							</svg>
							<span>0123 456 789</span>
						</div>
						<div class="flex items-center space-x-1">
							<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
								></path>
							</svg>
							<span><EMAIL></span>
						</div>
					</div>
					<div class="flex items-center space-x-2">
						<svg class="h-4 w-4 cursor-pointer text-blue-600" fill="currentColor" viewBox="0 0 24 24">
							<path
								d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"
							/>
						</svg>
						<svg class="h-4 w-4 cursor-pointer text-red-600" fill="currentColor" viewBox="0 0 24 24">
							<path
								d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"
							/>
						</svg>
						<svg class="h-4 w-4 cursor-pointer text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
							></path>
						</svg>
					</div>
				</div>

				<!-- Main navigation -->
				<nav class="flex items-center justify-between py-4">
					<div class="flex items-center space-x-2">
						<div class="flex h-12 w-12 items-center justify-center rounded-lg bg-teal-600">
							<svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
								></path>
							</svg>
						</div>
						<span class="text-xl font-bold text-teal-600">BỆNH VIỆN</span>
					</div>

					<div class="hidden items-center space-x-8 md:flex">
						<a href="#" class="font-medium text-gray-700 transition-colors hover:text-teal-600">Trang chủ</a>
						<a href="#" class="text-gray-700 transition-colors hover:text-teal-600">Giới thiệu</a>
						<a href="#" class="text-gray-700 transition-colors hover:text-teal-600">Dịch vụ</a>
						<a href="#" class="text-gray-700 transition-colors hover:text-teal-600">Bác sĩ</a>
						<a href="#" class="text-gray-700 transition-colors hover:text-teal-600">Tin tức</a>
						<a href="#" class="text-gray-700 transition-colors hover:text-teal-600">Liên hệ</a>
					</div>

					<button class="rounded-lg bg-teal-600 px-6 py-2 text-white transition-colors hover:bg-teal-700">Đặt lịch khám</button>
				</nav>
			</div>
		</header>

		<!-- Hero Section -->
		<section class="relative bg-gradient-to-r from-teal-50 to-blue-50 py-20">
			<div class="container mx-auto px-4">
				<div class="grid items-center gap-12 md:grid-cols-2">
					<div>
						<h1 class="mb-4 text-5xl font-bold text-gray-800">
							{{ heroData.title }}
						</h1>
						<h2 class="mb-6 text-3xl font-semibold text-teal-600">
							{{ heroData.subtitle }}
						</h2>
						<p class="mb-8 text-lg text-gray-600">
							{{ heroData.description }}
						</p>
						<div class="flex space-x-4">
							<button class="rounded-lg bg-teal-600 px-8 py-3 text-white transition-colors hover:bg-teal-700">Đặt lịch ngay</button>
							<button class="rounded-lg border border-teal-600 px-8 py-3 text-teal-600 transition-colors hover:bg-teal-50">Tìm hiểu thêm</button>
						</div>
					</div>
					<div class="relative">
						<img
							src="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
							alt="Medical team"
							class="h-96 w-full rounded-lg object-cover shadow-2xl"
						/>
					</div>
				</div>
			</div>
		</section>

		<!-- Services Section -->
		<section class="bg-white py-16">
			<div class="container mx-auto px-4">
				<div class="mb-12 text-center">
					<h2 class="mb-4 text-3xl font-bold text-gray-800">TẠI SAO CHỌN BỆNH VIỆN CHÚNG TÔI?</h2>
					<p class="mx-auto max-w-2xl text-gray-600">Chúng tôi cung cấp dịch vụ y tế chất lượng cao với đội ngũ chuyên gia giàu kinh nghiệm</p>
				</div>

				<div class="grid gap-8 md:grid-cols-3">
					<div v-for="service in services" :key="service.id" class="overflow-hidden rounded-lg bg-white shadow-lg transition-shadow hover:shadow-xl">
						<img
							:src="`https://images.unsplash.com/photo-${service.id === 1 ? '1576091160399-112ba8d25d1f' : service.id === 2 ? '**********-5c350d0d3c56' : '1582750433449-648ed127bb54'}?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80`"
							:alt="service.title"
							class="h-48 w-full object-cover"
						/>
						<div class="p-6">
							<h3 class="mb-3 text-xl font-semibold text-gray-800">
								{{ service.title }}
							</h3>
							<p class="mb-4 text-gray-600">
								{{ service.description }}
							</p>
							<button class="w-full rounded-lg border border-teal-600 py-2 text-teal-600 transition-colors hover:bg-teal-50">Xem thêm</button>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- Doctors Section -->
		<section class="bg-gray-50 py-16">
			<div class="container mx-auto px-4">
				<div class="mb-12 text-center">
					<h2 class="mb-4 text-3xl font-bold text-gray-800">ĐỘI NGŨ BÁC SĨ NỔI TIẾNG</h2>
					<p class="mx-auto max-w-2xl text-gray-600">Đội ngũ bác sĩ giàu kinh nghiệm, tận tâm với nghề và luôn cập nhật những kiến thức y khoa mới nhất</p>
				</div>

				<div class="grid gap-8 md:grid-cols-3">
					<div v-for="doctor in doctors" :key="doctor.id" class="overflow-hidden rounded-lg bg-white shadow-lg transition-shadow hover:shadow-xl">
						<img
							:src="`https://images.unsplash.com/photo-${doctor.id === 1 ? '1612349317150-e3d251d8e74e' : doctor.id === 2 ? '1559839734-2b71ea197ec2' : '1582750433449-648ed127bb54'}?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80`"
							:alt="doctor.name"
							class="h-64 w-full object-cover"
						/>
						<div class="p-6">
							<h3 class="mb-2 text-xl font-semibold text-gray-800">
								{{ doctor.name }}
							</h3>
							<p class="mb-2 font-medium text-teal-600">
								{{ doctor.specialty }}
							</p>
							<p class="text-gray-600">
								{{ doctor.experience }}
							</p>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- News Section -->
		<section class="bg-white py-16">
			<div class="container mx-auto px-4">
				<div class="mb-12 text-center">
					<h2 class="mb-4 text-3xl font-bold text-gray-800">TIN TỨC Y TẾ</h2>
					<p class="mx-auto max-w-2xl text-gray-600">Cập nhật những thông tin y tế mới nhất và hữu ích cho sức khỏe của bạn</p>
				</div>

				<div class="grid gap-8 md:grid-cols-2">
					<div v-for="article in news" :key="article.id" class="overflow-hidden rounded-lg bg-white shadow-lg transition-shadow hover:shadow-xl">
						<img
							:src="`https://images.unsplash.com/photo-${article.id === 1 ? '**********-5c350d0d3c56' : '1576091160399-112ba8d25d1f'}?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80`"
							:alt="article.title"
							class="h-48 w-full object-cover"
						/>
						<div class="p-6">
							<div class="mb-2 text-sm text-gray-500">{{ article.date }}</div>
							<h3 class="mb-3 text-xl font-semibold text-gray-800">
								{{ article.title }}
							</h3>
							<p class="mb-4 text-gray-600">
								{{ article.excerpt }}
							</p>
							<button class="font-medium text-teal-600 transition-colors hover:text-teal-700">Đọc thêm →</button>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- Contact Section -->
		<section class="bg-teal-600 py-16 text-white">
			<div class="container mx-auto px-4">
				<div class="mb-12 text-center">
					<h2 class="mb-4 text-3xl font-bold">ĐẶT LỊCH HẸN NGAY HÔM NAY</h2>
					<p class="mx-auto max-w-2xl">Liên hệ với chúng tôi để được tư vấn và đặt lịch khám bệnh một cách nhanh chóng và thuận tiện</p>
				</div>

				<div class="grid gap-12 md:grid-cols-2">
					<!-- Contact Info -->
					<div>
						<h3 class="mb-6 text-2xl font-semibold">Thông tin liên hệ</h3>
						<div class="space-y-4">
							<div class="flex items-center space-x-3">
								<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
								</svg>
								<span>123 Đường ABC, Quận 1, TP.HCM</span>
							</div>
							<div class="flex items-center space-x-3">
								<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
									></path>
								</svg>
								<span>0123 456 789</span>
							</div>
							<div class="flex items-center space-x-3">
								<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
									></path>
								</svg>
								<span><EMAIL></span>
							</div>
							<div class="flex items-center space-x-3">
								<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								<span>Thứ 2 - Chủ nhật: 7:00 - 22:00</span>
							</div>
						</div>
					</div>

					<!-- Contact Form -->
					<div>
						<h3 class="mb-6 text-2xl font-semibold">Gửi tin nhắn</h3>
						<form @submit.prevent="submitContact" class="space-y-4">
							<div>
								<input
									v-model="contactForm.name"
									type="text"
									placeholder="Họ và tên"
									class="w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-800 focus:border-teal-500 focus:outline-none"
									required
								/>
							</div>
							<div>
								<input
									v-model="contactForm.phone"
									type="tel"
									placeholder="Số điện thoại"
									class="w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-800 focus:border-teal-500 focus:outline-none"
									required
								/>
							</div>
							<div>
								<input
									v-model="contactForm.email"
									type="email"
									placeholder="Email"
									class="w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-800 focus:border-teal-500 focus:outline-none"
									required
								/>
							</div>
							<div>
								<textarea
									v-model="contactForm.message"
									placeholder="Tin nhắn"
									rows="4"
									class="w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-800 focus:border-teal-500 focus:outline-none"
									required
								></textarea>
							</div>
							<button type="submit" class="w-full rounded-lg bg-white px-6 py-3 font-semibold text-teal-600 transition-colors hover:bg-gray-100">Gửi tin nhắn</button>
						</form>
					</div>
				</div>
			</div>
		</section>

		<!-- Footer -->
		<footer class="bg-gray-800 py-12 text-white">
			<div class="container mx-auto px-4">
				<div class="grid gap-8 md:grid-cols-4">
					<!-- Company Info -->
					<div>
						<div class="mb-4 flex items-center space-x-2">
							<div class="flex h-10 w-10 items-center justify-center rounded-lg bg-teal-600">
								<svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
									></path>
								</svg>
							</div>
							<span class="text-lg font-bold">BỆNH VIỆN</span>
						</div>
						<p class="text-gray-300">Chúng tôi cam kết mang đến dịch vụ y tế chất lượng cao với đội ngũ bác sĩ giàu kinh nghiệm.</p>
					</div>

					<!-- Quick Links -->
					<div>
						<h4 class="mb-4 text-lg font-semibold">Liên kết nhanh</h4>
						<ul class="space-y-2 text-gray-300">
							<li><a href="#" class="transition-colors hover:text-white">Trang chủ</a></li>
							<li><a href="#" class="transition-colors hover:text-white">Giới thiệu</a></li>
							<li><a href="#" class="transition-colors hover:text-white">Dịch vụ</a></li>
							<li><a href="#" class="transition-colors hover:text-white">Bác sĩ</a></li>
							<li><a href="#" class="transition-colors hover:text-white">Tin tức</a></li>
						</ul>
					</div>

					<!-- Services -->
					<div>
						<h4 class="mb-4 text-lg font-semibold">Dịch vụ</h4>
						<ul class="space-y-2 text-gray-300">
							<li><a href="#" class="transition-colors hover:text-white">Khám tổng quát</a></li>
							<li><a href="#" class="transition-colors hover:text-white">Chẩn đoán hình ảnh</a></li>
							<li><a href="#" class="transition-colors hover:text-white">Xét nghiệm</a></li>
							<li><a href="#" class="transition-colors hover:text-white">Cấp cứu 24/7</a></li>
							<li><a href="#" class="transition-colors hover:text-white">Tư vấn trực tuyến</a></li>
						</ul>
					</div>

					<!-- Contact -->
					<div>
						<h4 class="mb-4 text-lg font-semibold">Liên hệ</h4>
						<div class="space-y-2 text-gray-300">
							<p>123 Đường ABC, Quận 1, TP.HCM</p>
							<p>Điện thoại: 0123 456 789</p>
							<p>Email: <EMAIL></p>
							<div class="mt-4 flex space-x-4">
								<svg class="h-6 w-6 cursor-pointer text-blue-400 hover:text-blue-300" fill="currentColor" viewBox="0 0 24 24">
									<path
										d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"
									/>
								</svg>
								<svg class="h-6 w-6 cursor-pointer text-red-400 hover:text-red-300" fill="currentColor" viewBox="0 0 24 24">
									<path
										d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"
									/>
								</svg>
							</div>
						</div>
					</div>
				</div>

				<div class="mt-8 border-t border-gray-700 pt-8 text-center text-gray-400">
					<p>&copy; 2024 Bệnh viện. Tất cả quyền được bảo lưu.</p>
				</div>
			</div>
		</footer>
	</div>
</template>

<style lang="scss" scoped></style>
