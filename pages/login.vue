<script lang="ts" setup>
import { ref } from 'vue'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Eye, EyeOff, Lock, User } from 'lucide-vue-next'

// Form data
const loginForm = ref({
  username: '',
  password: '',
  rememberPassword: false
})

// Password visibility toggle
const showPassword = ref(false)

// Loading state
const isLoading = ref(false)

// Form validation
const errors = ref({
  username: '',
  password: ''
})

// Form handlers
const validateForm = () => {
  errors.value = { username: '', password: '' }
  let isValid = true

  if (!loginForm.value.username.trim()) {
    errors.value.username = 'Username is required'
    isValid = false
  }

  if (!loginForm.value.password.trim()) {
    errors.value.password = 'Password is required'
    isValid = false
  } else if (loginForm.value.password.length < 6) {
    errors.value.password = 'Password must be at least 6 characters'
    isValid = false
  }

  return isValid
}

const handleLogin = async () => {
  if (!validateForm()) return

  isLoading.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    console.log('Login attempt:', {
      username: loginForm.value.username,
      password: loginForm.value.password,
      rememberPassword: loginForm.value.rememberPassword
    })
    
    // Handle successful login here
    // For example: await $router.push('/dashboard')
    alert('Login successful!')
    
  } catch (error) {
    console.error('Login failed:', error)
    alert('Login failed. Please try again.')
  } finally {
    isLoading.value = false
  }
}

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center p-4">
    <div class="w-full max-w-md bg-white rounded-lg shadow-lg border border-gray-200 p-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Welcome Back</h1>
        <p class="text-gray-600">Please sign in to your account</p>
      </div>

      <!-- Login Form -->
      <form @submit.prevent="handleLogin" class="space-y-6">
        <!-- Username Field -->
        <div class="space-y-2">
          <Label for="username" class="text-sm font-medium text-gray-700">
            Username
          </Label>
          <div class="relative">
            <User class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="username"
              v-model="loginForm.username"
              type="text"
              placeholder="Enter your username"
              class="pl-10"
              :class="{ 'border-red-500 focus-visible:ring-red-500': errors.username }"
              :disabled="isLoading"
            />
          </div>
          <p v-if="errors.username" class="text-sm text-red-600">
            {{ errors.username }}
          </p>
        </div>

        <!-- Password Field -->
        <div class="space-y-2">
          <Label for="password" class="text-sm font-medium text-gray-700">
            Password
          </Label>
          <div class="relative">
            <Lock class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="password"
              v-model="loginForm.password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="Enter your password"
              class="pl-10 pr-10"
              :class="{ 'border-red-500 focus-visible:ring-red-500': errors.password }"
              :disabled="isLoading"
            />
            <button
              type="button"
              @click="togglePasswordVisibility"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
              :disabled="isLoading"
            >
              <Eye v-if="!showPassword" class="h-4 w-4" />
              <EyeOff v-else class="h-4 w-4" />
            </button>
          </div>
          <p v-if="errors.password" class="text-sm text-red-600">
            {{ errors.password }}
          </p>
        </div>

        <!-- Remember Password Checkbox -->
        <div class="flex items-center space-x-2">
          <Checkbox
            id="remember"
            v-model="loginForm.rememberPassword"
            :disabled="isLoading"
          />
          <Label 
            for="remember" 
            class="text-sm text-gray-700 cursor-pointer select-none"
          >
            Remember password
          </Label>
        </div>

        <!-- Login Button -->
        <Button
          type="submit"
          class="w-full bg-primary text-primary-foreground hover:bg-primary/90 h-11"
          :disabled="isLoading"
        >
          <span v-if="!isLoading">Sign In</span>
          <span v-else class="flex items-center justify-center">
            <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Signing in...
          </span>
        </Button>
      </form>

      <!-- Footer Links -->
      <div class="mt-6 text-center space-y-2">
        <a href="#" class="text-sm text-primary hover:text-primary/80 transition-colors">
          Forgot your password?
        </a>
        <div class="text-sm text-gray-600">
          Don't have an account? 
          <a href="#" class="text-primary hover:text-primary/80 transition-colors font-medium">
            Sign up
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
