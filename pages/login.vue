<script lang="ts" setup>
	import { ref } from 'vue'
	import { Button } from '@/components/ui/button'
	import { Input } from '@/components/ui/input'
	import { Label } from '@/components/ui/label'
	import { Checkbox } from '@/components/ui/checkbox'
	import { Eye, EyeOff, Lock, User } from 'lucide-vue-next'

	// Form data
	const loginForm = ref({
		walletAddress: '',
		password: '',
		rememberPassword: false
	})

	// Password visibility toggle
	const showPassword = ref(false)

	// Loading state
	const isLoading = ref(false)

	// Form validation
	const errors = ref({
		walletAddress: '',
		password: '',
		general: ''
	})

	// Form handlers
	const validateForm = () => {
		errors.value = { walletAddress: '', password: '', general: '' }
		let isValid = true

		if (!loginForm.value.walletAddress.trim()) {
			errors.value.walletAddress = 'Wallet address is required'
			isValid = false
		} else {
			// Basic Cardano address validation
			const cardanoAddressRegex = /^addr(_test)?1[a-z0-9]+$/
			if (!cardanoAddressRegex.test(loginForm.value.walletAddress)) {
				errors.value.walletAddress = 'Invalid wallet address format'
				isValid = false
			}
		}

		if (!loginForm.value.password.trim()) {
			errors.value.password = 'Password is required'
			isValid = false
		} else if (loginForm.value.password.length < 8) {
			errors.value.password = 'Password must be at least 8 characters'
			isValid = false
		}

		return isValid
	}

	const handleLogin = async () => {
		if (!validateForm()) return

		isLoading.value = true
		errors.value.general = ''

		try {
			// Call the login API
			const { data } = await $fetch('/api/auth/login', {
				method: 'POST',
				body: {
					walletAddress: loginForm.value.walletAddress,
					password: loginForm.value.password
				}
			})

			console.log('Login successful:', data)

			// Store token if remember password is checked
			if (loginForm.value.rememberPassword) {
				// In a real app, you might use a secure cookie or localStorage
				localStorage.setItem('authToken', data.token)
				localStorage.setItem('walletAddress', loginForm.value.walletAddress)
			}

			// Handle successful login here
			// For example: await navigateTo('/dashboard')
			alert(`Welcome back, ${data.user.name}!`)
		} catch (error: any) {
			console.error('Login failed:', error)

			if (error.statusCode === 401) {
				errors.value.general = 'Invalid wallet address or password'
			} else if (error.statusCode === 400) {
				errors.value.general = error.statusMessage || 'Invalid input'
			} else {
				errors.value.general = 'Login failed. Please try again.'
			}
		} finally {
			isLoading.value = false
		}
	}

	const togglePasswordVisibility = () => {
		showPassword.value = !showPassword.value
	}

	const fillDemoCredentials = () => {
		loginForm.value.walletAddress = 'addr_test1qrsx72hrv8ens90hwkezg7ysyhwvcjmyzdveyf88ppq7a0lwu7gv0wuuf9lhzm7wclvj5ntgcfa53j0rqxmu237x20xsne56q3'
		loginForm.value.password = 'Password123!'
	}
</script>

<template>
	<div class="flex min-h-screen items-center justify-center bg-gray-50 p-4">
		<div class="w-full max-w-md rounded-lg border border-gray-200 bg-white p-8 shadow-lg">
			<!-- Header -->
			<div class="mb-8 text-center">
				<h1 class="mb-2 text-2xl font-bold text-gray-900">Welcome Back</h1>
				<p class="text-gray-600">Please sign in to your account</p>
			</div>

			<!-- Login Form -->
			<form @submit.prevent="handleLogin" class="space-y-6">
				<!-- General Error Message -->
				<div v-if="errors.general" class="rounded-md border border-red-200 bg-red-50 p-3">
					<p class="text-sm text-red-600">{{ errors.general }}</p>
				</div>

				<!-- Wallet Address Field -->
				<div class="space-y-2">
					<Label for="walletAddress" class="text-sm font-medium text-gray-700"> Wallet Address </Label>
					<div class="relative">
						<User class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
						<Input
							id="walletAddress"
							v-model="loginForm.walletAddress"
							type="text"
							placeholder="Enter your wallet address"
							class="pl-10"
							:class="{ 'border-red-500 focus-visible:ring-red-500': errors.walletAddress }"
							:disabled="isLoading"
						/>
					</div>
					<p v-if="errors.walletAddress" class="text-sm text-red-600">
						{{ errors.walletAddress }}
					</p>
				</div>

				<!-- Password Field -->
				<div class="space-y-2">
					<Label for="password" class="text-sm font-medium text-gray-700"> Password </Label>
					<div class="relative">
						<Lock class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
						<Input
							id="password"
							v-model="loginForm.password"
							:type="showPassword ? 'text' : 'password'"
							placeholder="Enter your password"
							class="pl-10 pr-10"
							:class="{ 'border-red-500 focus-visible:ring-red-500': errors.password }"
							:disabled="isLoading"
						/>
						<button
							type="button"
							@click="togglePasswordVisibility"
							class="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 transition-colors hover:text-gray-600"
							:disabled="isLoading"
						>
							<Eye v-if="!showPassword" class="h-4 w-4" />
							<EyeOff v-else class="h-4 w-4" />
						</button>
					</div>
					<p v-if="errors.password" class="text-sm text-red-600">
						{{ errors.password }}
					</p>
				</div>

				<!-- Remember Password Checkbox -->
				<div class="flex items-center space-x-2">
					<Checkbox id="remember" v-model="loginForm.rememberPassword" :disabled="isLoading" />
					<Label for="remember" class="cursor-pointer select-none text-sm text-gray-700"> Remember password </Label>
				</div>

				<!-- Login Button -->
				<Button type="submit" class="h-11 w-full bg-primary text-primary-foreground hover:bg-primary/90" :disabled="isLoading">
					<span v-if="!isLoading">Sign In</span>
					<span v-else class="flex items-center justify-center">
						<svg class="-ml-1 mr-3 h-4 w-4 animate-spin text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
							<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
							<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
						</svg>
						Signing in...
					</span>
				</Button>
			</form>

			<!-- Demo Button -->
			<div class="mt-4 text-center">
				<Button type="button" variant="outline" @click="fillDemoCredentials" class="w-full" :disabled="isLoading"> Use Demo Credentials </Button>
			</div>

			<!-- Footer Links -->
			<div class="mt-6 space-y-2 text-center">
				<a href="#" class="text-sm text-primary transition-colors hover:text-primary/80"> Forgot your password? </a>
				<div class="text-sm text-gray-600">
					Don't have an account?
					<a href="#" class="font-medium text-primary transition-colors hover:text-primary/80"> Sign up </a>
				</div>
			</div>
		</div>
	</div>
</template>

<style lang="scss" scoped></style>
