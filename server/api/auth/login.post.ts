export default defineEventHandler(async event => {
	try {
		// Parse the request body
		const body = await readBody(event)

		// Validate required fields
		if (!body.walletAddress || !body.password) {
			throw createError({
				statusCode: 400,
				statusMessage: 'Wallet address and password are required'
			})
		}

		// Validate wallet address format (basic Cardano address validation)
		const cardanoAddressRegex = /^addr(_test)?1[a-z0-9]+$/
		if (!cardanoAddressRegex.test(body.walletAddress)) {
			throw createError({
				statusCode: 400,
				statusMessage: 'Invalid wallet address format'
			})
		}

		// Validate password strength (basic validation)
		if (body.password.length < 8) {
			throw createError({
				statusCode: 400,
				statusMessage: 'Password must be at least 8 characters long'
			})
		}

		// Simulate user lookup and authentication
		// In a real application, you would:
		// 1. Hash the password and compare with stored hash
		// 2. Look up user by wallet address in database
		// 3. Verify password against stored hash
		// 4. Generate JWT token or session

		const mockUsers = [
			{
				walletAddress: 'addr_test1qrsx72hrv8ens90hwkezg7ysyhwvcjmyzdveyf88ppq7a0lwu7gv0wuuf9lhzm7wclvj5ntgcfa53j0rqxmu237x20xsne56q3',
				password: 'Password123!',
				id: 1,
				name: '<PERSON>',
				email: '<EMAIL>',
				role: 'user'
			},
			{
				walletAddress: 'addr_test1qp8x9k2l3m4n5o6p7q8r9s0t1u2v3w4x5y6z7a8b9c0d1e2f3g4h5i6j7k8l9m0n1o2p3q4r5s6t7u8v9w0x1y2z3a4b5c6d7e8f9',
				password: 'SecurePass456!',
				id: 2,
				name: 'Jane <PERSON>',
				email: '<EMAIL>',
				role: 'admin'
			}
		]

		// Find user by wallet address
		const user = mockUsers.find(u => u.walletAddress === body.walletAddress)

		if (!user) {
			throw createError({
				statusCode: 401,
				statusMessage: 'Invalid wallet address or password'
			})
		}

		// Verify password (in production, use bcrypt or similar)
		if (user.password !== body.password) {
			throw createError({
				statusCode: 401,
				statusMessage: 'Invalid wallet address or password'
			})
		}

		// Simulate token generation (in production, use JWT)
		const token = Buffer.from(`${user.id}:${Date.now()}`).toString('base64')

		// Return success response with user data and token
		return {
			success: true,
			message: 'Login successful',
			data: {
				user: {
					id: user.id,
					name: user.name,
					email: user.email,
					walletAddress: user.walletAddress,
					role: user.role
				},
				token: token,
				expiresIn: '24h'
			}
		}
	} catch (error) {
		// Handle any unexpected errors
		if (error.statusCode) {
			throw error
		}

		throw createError({
			statusCode: 500,
			statusMessage: 'Internal server error'
		})
	}
})
