<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { Check } from 'lucide-vue-next'

interface Props {
  class?: HTMLAttributes['class']
  modelValue?: boolean
  defaultChecked?: boolean
  disabled?: boolean
  id?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

defineEmits<{
  'update:modelValue': [value: boolean]
}>()
</script>

<template>
  <div class="relative inline-flex items-center">
    <input
      :id="id"
      type="checkbox"
      :checked="modelValue ?? defaultChecked"
      :disabled="disabled"
      :class="cn(
        'peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground',
        'sr-only'
      )"
      @change="$emit('update:modelValue', ($event.target as HTMLInputElement).checked)"
    />
    <div
      :class="cn(
        'h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 transition-colors',
        modelValue || defaultChecked ? 'bg-primary text-primary-foreground' : 'bg-background',
        disabled && 'cursor-not-allowed opacity-50',
        props.class
      )"
      @click="!disabled && $emit('update:modelValue', !(modelValue ?? defaultChecked))"
    >
      <Check 
        v-if="modelValue || defaultChecked"
        class="h-3 w-3 text-current"
      />
    </div>
  </div>
</template>
