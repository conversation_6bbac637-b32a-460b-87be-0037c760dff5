<script lang="ts" setup>
import { computed } from 'vue'

interface CardProps {
  variant?: 'default' | 'outlined' | 'elevated' | 'filled'
  size?: 'sm' | 'md' | 'lg'
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  hover?: boolean
  clickable?: boolean
  disabled?: boolean
  class?: string
}

const props = withDefaults(defineProps<CardProps>(), {
  variant: 'default',
  size: 'md',
  rounded: 'lg',
  shadow: 'md',
  hover: false,
  clickable: false,
  disabled: false,
  class: ''
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const cardClasses = computed(() => {
  const classes = ['card']
  
  // Base styles
  classes.push('bg-white border transition-all duration-200')
  
  // Variant styles
  switch (props.variant) {
    case 'outlined':
      classes.push('border-gray-200 bg-transparent')
      break
    case 'elevated':
      classes.push('border-transparent shadow-lg')
      break
    case 'filled':
      classes.push('border-transparent bg-gray-50')
      break
    default:
      classes.push('border-gray-200')
  }
  
  // Size styles
  switch (props.size) {
    case 'sm':
      classes.push('p-3')
      break
    case 'lg':
      classes.push('p-8')
      break
    default:
      classes.push('p-6')
  }
  
  // Rounded styles
  switch (props.rounded) {
    case 'none':
      classes.push('rounded-none')
      break
    case 'sm':
      classes.push('rounded-sm')
      break
    case 'md':
      classes.push('rounded-md')
      break
    case 'lg':
      classes.push('rounded-lg')
      break
    case 'xl':
      classes.push('rounded-xl')
      break
    case 'full':
      classes.push('rounded-full')
      break
  }
  
  // Shadow styles
  switch (props.shadow) {
    case 'none':
      classes.push('shadow-none')
      break
    case 'sm':
      classes.push('shadow-sm')
      break
    case 'md':
      classes.push('shadow-md')
      break
    case 'lg':
      classes.push('shadow-lg')
      break
    case 'xl':
      classes.push('shadow-xl')
      break
  }
  
  // Interactive styles
  if (props.clickable || props.hover) {
    classes.push('cursor-pointer')
    if (props.hover) {
      classes.push('hover:shadow-lg hover:-translate-y-1')
    }
  }
  
  // Disabled state
  if (props.disabled) {
    classes.push('opacity-50 cursor-not-allowed')
  }
  
  // Custom classes
  if (props.class) {
    classes.push(props.class)
  }
  
  return classes.join(' ')
})

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && (props.clickable || props.hover)) {
    emit('click', event)
  }
}
</script>

<template>
  <div 
    :class="cardClasses"
    @click="handleClick"
  >
    <slot />
  </div>
</template>

<style scoped>
.card {
  @apply relative overflow-hidden;
}
</style>
