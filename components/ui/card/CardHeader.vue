<script lang="ts" setup>
import { computed } from 'vue'

interface CardHeaderProps {
  class?: string
  padding?: 'none' | 'sm' | 'md' | 'lg'
  border?: boolean
}

const props = withDefaults(defineProps<CardHeaderProps>(), {
  class: '',
  padding: 'md',
  border: false
})

const headerClasses = computed(() => {
  const classes = ['card-header']
  
  // Padding styles
  switch (props.padding) {
    case 'none':
      classes.push('p-0')
      break
    case 'sm':
      classes.push('p-3')
      break
    case 'lg':
      classes.push('p-6')
      break
    default:
      classes.push('p-4')
  }
  
  // Border
  if (props.border) {
    classes.push('border-b border-gray-200')
  }
  
  // Custom classes
  if (props.class) {
    classes.push(props.class)
  }
  
  return classes.join(' ')
})
</script>

<template>
  <div :class="headerClasses">
    <slot />
  </div>
</template>

<style scoped>
.card-header {
  @apply flex flex-col space-y-1.5;
}
</style>
