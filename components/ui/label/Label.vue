<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { Primitive, type PrimitiveProps } from 'radix-vue'

interface Props extends PrimitiveProps {
  class?: HTMLAttributes['class']
  for?: string
}

const props = withDefaults(defineProps<Props>(), {
  as: 'label'
})
</script>

<template>
  <Primitive
    :as="as"
    :as-child="asChild"
    :for="for"
    :class="cn(
      'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
      props.class
    )"
  >
    <slot />
  </Primitive>
</template>
