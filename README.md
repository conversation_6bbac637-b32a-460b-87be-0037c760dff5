# Nuxt Base Project

A modern Nuxt 3 application with TypeScript, Tailwind CSS, and Shadcn/ui components. This project serves as a solid foundation for building scalable web applications with Vue 3 and Nuxt 3.

## 🚀 Features

- **Nuxt 3** - The latest version of the Vue.js framework
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Shadcn/ui** - Beautiful and accessible UI components
- **ESLint & Prettier** - Code linting and formatting
- **SCSS Support** - Enhanced styling capabilities
- **Color Mode** - Dark/light theme support
- **Lucide Icons** - Beautiful icon library

## 📁 Project Structure

```
nuxt-base/
├── assets/                 # Static assets (SCSS, images, etc.)
│   └── scss/
│       └── index.scss      # Main SCSS entry point
├── components/             # Vue components
│   └── ui/                 # Shadcn/ui components
│       ├── button/         # Button component variants
│       └── dialog/         # Dialog component variants
├── interfaces/             # TypeScript interfaces and types
│   └── index.ts           # Shared type definitions
├── layouts/               # Nuxt layouts
│   └── default.vue        # Default layout template
├── lib/                   # Utility libraries
│   └── utils.ts          # Helper functions and utilities
├── pages/                 # File-based routing pages
│   └── index.vue         # Home page
├── public/               # Static files served at root
│   ├── favicon.ico       # Site favicon
│   └── robots.txt        # SEO robots file
├── server/               # Server-side code
│   └── tsconfig.json     # Server TypeScript config
├── app.vue              # Root Vue component
├── nuxt.config.ts       # Nuxt configuration
├── package.json         # Dependencies and scripts
├── tailwind.config.js   # Tailwind CSS configuration
├── tsconfig.json        # TypeScript configuration
├── eslint.config.mjs    # ESLint configuration
├── components.json      # Shadcn/ui components config
└── pnpm-lock.yaml      # Package manager lock file
```

## 🛠️ Tech Stack

### Core Framework
- **Nuxt 3** (v3.15.0) - Full-stack Vue framework
- **Vue 3** (v3.5.13) - Progressive JavaScript framework
- **TypeScript** (v5.7.2) - Static type checking

### Styling & UI
- **Tailwind CSS** (v6.12.2) - Utility-first CSS framework
- **Shadcn/ui** (v0.11.3) - Component library built on Radix Vue
- **Radix Vue** (v1.9.12) - Unstyled, accessible components
- **SCSS** (v1.83.0) - CSS preprocessor
- **Tailwind Animate** - Animation utilities

### Development Tools
- **ESLint** (v9.17.0) - Code linting
- **Prettier** (v3.4.2) - Code formatting
- **@antfu/eslint-config** - Opinionated ESLint config

### Utilities
- **Class Variance Authority** - Component variant management
- **clsx** & **tailwind-merge** - Conditional class utilities
- **Lucide Vue Next** - Icon library

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- pnpm (recommended) or npm/yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd nuxt-base
```

2. Install dependencies:
```bash
# Using pnpm (recommended)
pnpm install

# Or using npm
npm install

# Or using yarn
yarn install
```

### Development

Start the development server on `http://localhost:3000`:

```bash
# Using pnpm
pnpm dev

# Using npm
npm run dev

# Using yarn
yarn dev
```

### Building for Production

Build the application for production:

```bash
# Using pnpm
pnpm build

# Using npm
npm run build

# Using yarn
yarn build
```

Preview the production build locally:

```bash
# Using pnpm
pnpm preview

# Using npm
npm run preview

# Using yarn
yarn preview
```

## 📝 Available Scripts

- `dev` - Start development server
- `build` - Build for production
- `generate` - Generate static site
- `preview` - Preview production build
- `postinstall` - Prepare Nuxt (runs automatically after install)

## 🎨 Styling

This project uses Tailwind CSS for styling with additional SCSS support. The main stylesheet is located at `assets/scss/index.scss`.

### Shadcn/ui Components

The project includes Shadcn/ui components configured in the `components/ui` directory. You can add new components using:

```bash
npx shadcn-vue@latest add <component-name>
```

## 🔧 Configuration

### Nuxt Configuration

The main configuration is in `nuxt.config.ts`:
- Tailwind CSS module enabled
- Shadcn/ui module configured
- SCSS preprocessing enabled
- Development tools enabled

### TypeScript

TypeScript is configured with strict settings in `tsconfig.json` for both client and server code.

## 📚 Learn More

- [Nuxt 3 Documentation](https://nuxt.com/docs)
- [Vue 3 Documentation](https://vuejs.org/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Shadcn/ui Documentation](https://www.shadcn-vue.com/)

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
